from nonebot import get_driver, get_plugin_config, require
from nonebot.plugin import PluginMetadata

require("nonebot_plugin_apscheduler")
from nonebot_plugin_apscheduler import scheduler

from .config import Config

require("nonebot_plugin_htmlrender")
import json
import os
import re
import time
from typing import Annotated

import nonebot
from nonebot.adapters.onebot.v11.event import GroupMessageEvent as V11GroupMessageEvent
from nonebot.adapters.onebot.v12.event import GroupMessageEvent as V12GroupMessageEvent
from nonebot.internal.adapter.bot import Bot
from nonebot.params import RegexStr
from nonebot_plugin_saa import (
    Image as saaImage,
)
from nonebot_plugin_saa import (
    MessageFactory,
)

from .. import aiorequests
from ..handle_prefix import handle_prefix as handle_prefix

cache_path = os.path.join(os.path.dirname(__file__), "cache.json")

__plugin_meta = PluginMetadata(
    name="og_hunter",
    description="",
    usage="",
    config=Config,
)

global_config = get_driver().config
config = get_plugin_config(Config)

# 正则匹配网址
reg_command = nonebot.on_regex(r"(?i)https?://[^\s]+", flags=re.IGNORECASE, block=False, priority=999)
cache = json.loads(open(cache_path).read())


@scheduler.scheduled_job("interval", minutes=5)
async def save_cache():
    with open(cache_path, "w") as f:
        f.write(json.dumps(cache, ensure_ascii=False, indent=4))


@scheduler.scheduled_job("interval", hours=1)
async def clear_cache():
    for k, v in cache.items():
        if time.time() - v["time"] > 3600 * 24 * 7:
            del cache[k]


@reg_command.handle()
async def handle_first_receive(
    bot: Bot,
    event: V11GroupMessageEvent | V12GroupMessageEvent,
    url: Annotated[str, RegexStr()],
):
    if not url:
        return
    if "mihoyo" in url:
        return
    # 解析元数据
    meta = cache.get(url) or await get_meta(url)
    if not meta:
        return
    # 生成图片
    pic = saaImage(meta["image"]) if meta["image"] else None
    if pic:
        await MessageFactory([pic]).send()
        cache[url] = {
            "title": meta["title"],
            "description": meta["description"],
            "image": meta["image"],
            "time": time.time(),
        }
        json.dump(cache, open(cache_path, "w"), ensure_ascii=False, indent=4)


async def get_meta(url: str):
    # 获取网页源码，限制大小避免内存爆炸
    try:
        # 先发送 HEAD 请求检查 Content-Type 和 Content-Length
        head_response = await aiorequests.head(url)
        if head_response.status_code not in [200, 301, 302]:
            return None

        content_type = head_response.headers.get("content-type", "").lower()
        # 只处理 HTML 内容
        if not any(ct in content_type for ct in ["text/html", "text/plain", "application/xhtml"]):
            return None

        content_length = head_response.headers.get("content-length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 限制 10MB
            return None

        # 获取实际内容，设置超时和大小限制
        response = await aiorequests.get(url, timeout=10)
        if response.status_code != 200:
            return None

        # 分块读取，限制总大小
        html_chunks = []
        total_size = 0
        max_size = 5 * 1024 * 1024  # 5MB 限制

        async for chunk in response.iter_content(chunk_size=8192):
            total_size += len(chunk)
            if total_size > max_size:
                return None  # 文件太大，放弃
            html_chunks.append(chunk)

        html = b"".join(html_chunks).decode("utf-8", errors="ignore")

    except Exception:
        return None

    for func in [get_meta_og, get_meta_twitter]:
        meta = func(html)
        if meta:
            return meta


def get_meta_og(html: str):
    # 获取标题
    title = re.search(r"<title>(.*?)</title>", html)
    if title:
        title = title.group(1)
    else:
        title = "未知标题"
    # 获取描述
    description = re.search(r'<meta name="description" content="(.*?)"', html)
    if description:
        description = description.group(1)
    else:
        description = "未知描述"
    # 获取图片
    image = re.search(r'<meta property="og:image" content="(.*?)"', html)
    if image:
        image = image.group(1)
    else:
        image = None
        return None  # 没有图片就不显示了
    return {"title": title, "description": description, "image": image}


def get_meta_twitter(html: str):
    # 获取标题
    title = re.search(r'<meta name="twitter:title" content="(.*?)"', html)
    if title:
        title = title.group(1)
    else:
        title = "未知标题"
    # 获取描述
    description = re.search(r'<meta name="twitter:description" content="(.*?)"', html)
    if description:
        description = description.group(1)
    else:
        description = "未知描述"
    # 获取图片
    image = re.search(r'<meta name="twitter:image" content="(.*?)"', html)
    if image:
        image = image.group(1)
    else:
        image = None
        return None  # 没有图片就不显示了
    return {"title": title, "description": description, "image": image}
